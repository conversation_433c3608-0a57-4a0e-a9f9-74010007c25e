# AFM MCP Server

Model Context Protocol (MCP) server pro integraci s AFM (Alstanet Facility Management) REST API. Tento server umožňuje AI agentům přístup k AFM systému pro správu incident ticketů.

## Funkcionality

MCP server poskytuje následující tools pro práci s AFM tickety:

- **`afm_get_tickets`** - Získání seznamu všech ticketů nebo konkrétního ticketu podle ID
- **`afm_insert_ticket`** - Vytvoření nového incident ticketu
- **`afm_update_ticket`** - Aktualizace existujícího ticketu

## Požadavky

- Python 3.11+
- Docker (pro kontejnerizaci)
- Přístup k AFM REST API

## Instalace

### Lokální instalace

1. Klonujte repository:
```bash
git clone <repository-url>
cd afm-mcp-server
```

2. Vytvořte virtuální prostředí:
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# nebo
venv\Scripts\activate     # Windows
```

3. Nainstalujte z<PERSON>ti:
```bash
pip install -r requirements.txt
pip install -e .
```

4. Nakonfigurujte prostředí:
```bash
cp .env.example .env
# Upravte .env soubor podle potřeby
```

### Docker instalace

1. Buildněte Docker image:
```bash
docker build -t afm-mcp-server .
```

2. Nebo použijte Docker Compose:
```bash
docker-compose up --build
```

## Konfigurace

Server se konfiguruje pomocí environment variables:

| Proměnná | Popis | Výchozí hodnota |
|----------|-------|-----------------|
| `AFM_BASE_URL` | Base URL AFM API | `https://fmfullademo3.dev.alstanet.cz/api/v1` |
| `AFM_CLIENT_ID` | OAuth 2.0 Client ID | `e0cc897a-5b99-4cc9-984c-6ee0581c4169` |
| `AFM_CLIENT_SECRET` | OAuth 2.0 Client Secret | `DNfvkCXfsNGLKela6vFDMxjZcqPjSu0YxvxdZ5bGO0c=` |
| `AFM_TOKEN_URL` | OAuth 2.0 Token URL | `https://fmfullademo3.dev.alstanet.cz/api/v1/oauth2/token` |
| `MCP_SERVER_NAME` | Název MCP serveru | `afm-mcp-server` |
| `MCP_SERVER_VERSION` | Verze serveru | `0.1.0` |
| `LOG_LEVEL` | Úroveň logování | `INFO` |

## Použití

### Spuštění serveru

#### Lokálně
```bash
afm-mcp-server
```

#### Docker
```bash
docker run -it --rm afm-mcp-server
```

#### Docker Compose
```bash
# Produkční verze
docker-compose up afm-mcp-server

# Development verze
docker-compose --profile dev up afm-mcp-dev
```

### MCP Tools

#### 1. afm_get_tickets

Získání ticketů z AFM systému.

**Parametry:**
- `ticket_id` (optional): ID konkrétního ticketu
- `limit` (optional): Maximální počet ticketů (1-1000)
- `offset` (optional): Počet ticketů k přeskočení
- `filters` (optional): Dodatečné filtry

**Příklady:**
```json
// Získat všechny tickety
{
  "name": "afm_get_tickets",
  "arguments": {}
}

// Získat konkrétní ticket
{
  "name": "afm_get_tickets",
  "arguments": {
    "ticket_id": "12345"
  }
}

// Získat prvních 10 ticketů
{
  "name": "afm_get_tickets",
  "arguments": {
    "limit": 10,
    "offset": 0
  }
}
```

#### 2. afm_insert_ticket

Vytvoření nového incident ticketu.

**Povinné parametry:**
- `title`: Název/předmět ticketu
- `description`: Detailní popis incidentu

**Volitelné parametry:**
- `priority`: Priorita (Low, Medium, High, Critical)
- `category`: Kategorie incidentu
- `location`: Místo incidentu
- `reporter`: Osoba hlásící incident

**Příklad:**
```json
{
  "name": "afm_insert_ticket",
  "arguments": {
    "title": "Nefunkční klimatizace v kanceláři",
    "description": "Klimatizace v kanceláři 205 nefunguje, teplota je příliš vysoká",
    "priority": "High",
    "category": "HVAC",
    "location": "Kancelář 205",
    "reporter": "Jan Novák"
  }
}
```

#### 3. afm_update_ticket

Aktualizace existujícího ticketu.

**Povinné parametry:**
- `ticket_id`: ID ticketu k aktualizaci

**Volitelné parametry:**
- `title`: Aktualizovaný název
- `description`: Aktualizovaný popis
- `status`: Nový status
- `priority`: Nová priorita
- `category`: Nová kategorie
- `assigned_to`: Přiřazená osoba/tým
- `location`: Aktualizované místo

**Příklad:**
```json
{
  "name": "afm_update_ticket",
  "arguments": {
    "ticket_id": "12345",
    "status": "In Progress",
    "assigned_to": "Technický tým",
    "priority": "Medium"
  }
}
```

## Vývoj

### Testování

```bash
# Spuštění testů
pytest

# Spuštění testů s coverage
pytest --cov=afm_mcp_server

# Linting
ruff check src/
black --check src/
mypy src/
```

### Struktura projektu

```
afm-mcp-server/
├── src/
│   └── afm_mcp_server/
│       ├── __init__.py
│       ├── server.py          # Hlavní MCP server
│       ├── afm_client.py      # AFM REST API klient
│       ├── models.py          # Pydantic modely
│       └── tools.py           # MCP tools implementace
├── tests/                     # Testy
├── Dockerfile                 # Docker image
├── docker-compose.yml         # Docker Compose konfigurace
├── requirements.txt           # Python závislosti
├── pyproject.toml            # Projekt konfigurace
└── README.md                 # Dokumentace
```

## Troubleshooting

### Časté problémy

1. **Autentifikační chyby**
   - Zkontrolujte `AFM_CLIENT_ID` a `AFM_CLIENT_SECRET`
   - Ověřte dostupnost `AFM_TOKEN_URL`

2. **Síťové problémy**
   - Zkontrolujte dostupnost `AFM_BASE_URL`
   - Ověřte firewall nastavení

3. **Chyby při parsování dat**
   - Zkontrolujte logy pro detailní informace
   - Nastavte `LOG_LEVEL=DEBUG` pro více informací

### Logy

Server používá strukturované logování. Logy jsou ve formátu JSON a obsahují:
- Timestamp
- Log level
- Logger name
- Message
- Kontextové informace

## Licence

MIT License

## Podpora

Pro podporu a hlášení chyb vytvořte issue v repository.
