.PHONY: help install test lint format clean build run docker-build docker-run docker-test

# Default target
help:
	@echo "AFM MCP Server - Available commands:"
	@echo ""
	@echo "Development:"
	@echo "  install     - Install dependencies and package in development mode"
	@echo "  test        - Run tests"
	@echo "  test-conn   - Test AFM API connection"
	@echo "  lint        - Run linting (ruff, mypy)"
	@echo "  format      - Format code (black, isort)"
	@echo "  clean       - Clean build artifacts"
	@echo ""
	@echo "Running:"
	@echo "  run         - Run MCP server locally"
	@echo ""
	@echo "Docker:"
	@echo "  docker-build - Build Docker image"
	@echo "  docker-run   - Run MCP server in Docker"
	@echo "  docker-test  - Test AFM connection in Docker"
	@echo "  docker-dev   - Run development version with Docker Compose"
	@echo ""

# Development
install:
	pip install -r requirements.txt
	pip install -e .

test:
	pytest tests/ -v

test-conn:
	python test_connection.py

lint:
	ruff check src/
	mypy src/

format:
	black src/ tests/
	isort src/ tests/

clean:
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete

# Running
run:
	afm-mcp-server

# Docker
docker-build:
	docker build -t afm-mcp-server .

docker-run:
	docker run -it --rm afm-mcp-server

docker-test:
	docker run --rm afm-mcp-server python test_connection.py

docker-dev:
	docker-compose --profile dev up --build

docker-prod:
	docker-compose up --build

# Setup environment
setup:
	cp .env.example .env
	@echo "Please edit .env file with your configuration"
