"""MCP tools for AFM API integration."""

import json
from typing import Any, Dict, List, Optional

import structlog
from mcp.types import <PERSON><PERSON><PERSON><PERSON><PERSON>ult, TextContent, Tool
from pydantic import ValidationError

from .afm_client import AFMClient, AFMClientError
from .models import AFMTicketCreate, AFMTicketUpdate

logger = structlog.get_logger(__name__)


class AFMTools:
    """MCP tools for AFM API operations."""
    
    def __init__(self, afm_client: AFMClient):
        """Initialize AFM tools.
        
        Args:
            afm_client: AFM API client instance
        """
        self.afm_client = afm_client
    
    def get_tools(self) -> List[Tool]:
        """Get list of available MCP tools.
        
        Returns:
            List of MCP Tool definitions
        """
        return [
            Tool(
                name="afm_get_tickets",
                description="Get AFM tickets. Can retrieve all tickets or a specific ticket by ID.",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "ticket_id": {
                            "type": "string",
                            "description": "Optional: ID of specific ticket to retrieve. If not provided, returns all tickets."
                        },
                        "limit": {
                            "type": "integer",
                            "description": "Optional: Maximum number of tickets to return (when getting all tickets).",
                            "minimum": 1,
                            "maximum": 1000
                        },
                        "offset": {
                            "type": "integer",
                            "description": "Optional: Number of tickets to skip (for pagination).",
                            "minimum": 0
                        },
                        "filters": {
                            "type": "object",
                            "description": "Optional: Additional filters for ticket search (status, priority, etc.).",
                            "additionalProperties": True
                        }
                    },
                    "additionalProperties": False
                }
            ),
            Tool(
                name="afm_insert_ticket",
                description="Create a new AFM incident ticket.",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "title": {
                            "type": "string",
                            "description": "Title/subject of the ticket (required)."
                        },
                        "description": {
                            "type": "string",
                            "description": "Detailed description of the incident (required)."
                        },
                        "priority": {
                            "type": "string",
                            "description": "Priority level of the ticket (Low, Medium, High, Critical). Default: Medium.",
                            "enum": ["Low", "Medium", "High", "Critical"]
                        },
                        "category": {
                            "type": "string",
                            "description": "Category/type of the incident (optional)."
                        },
                        "location": {
                            "type": "string",
                            "description": "Location where incident occurred (optional)."
                        },
                        "reporter": {
                            "type": "string",
                            "description": "Person reporting the incident (optional)."
                        }
                    },
                    "required": ["title", "description"],
                    "additionalProperties": False
                }
            ),
            Tool(
                name="afm_update_ticket",
                description="Update an existing AFM incident ticket.",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "ticket_id": {
                            "type": "string",
                            "description": "ID of the ticket to update (required)."
                        },
                        "title": {
                            "type": "string",
                            "description": "Updated title/subject of the ticket."
                        },
                        "description": {
                            "type": "string",
                            "description": "Updated description of the incident."
                        },
                        "status": {
                            "type": "string",
                            "description": "Updated status of the ticket."
                        },
                        "priority": {
                            "type": "string",
                            "description": "Updated priority level (Low, Medium, High, Critical).",
                            "enum": ["Low", "Medium", "High", "Critical"]
                        },
                        "category": {
                            "type": "string",
                            "description": "Updated category/type of the incident."
                        },
                        "assigned_to": {
                            "type": "string",
                            "description": "Person/team assigned to the ticket."
                        },
                        "location": {
                            "type": "string",
                            "description": "Updated location where incident occurred."
                        }
                    },
                    "required": ["ticket_id"],
                    "additionalProperties": False
                }
            )
        ]
    
    async def handle_tool_call(self, name: str, arguments: Dict[str, Any]) -> CallToolResult:
        """Handle MCP tool call.
        
        Args:
            name: Name of the tool to call
            arguments: Tool arguments
            
        Returns:
            Tool execution result
        """
        try:
            if name == "afm_get_tickets":
                return await self._handle_get_tickets(arguments)
            elif name == "afm_insert_ticket":
                return await self._handle_insert_ticket(arguments)
            elif name == "afm_update_ticket":
                return await self._handle_update_ticket(arguments)
            else:
                return CallToolResult(
                    content=[TextContent(type="text", text=f"Unknown tool: {name}")],
                    isError=True
                )
        
        except Exception as e:
            logger.error("Tool execution failed", tool=name, error=str(e), arguments=arguments)
            return CallToolResult(
                content=[TextContent(type="text", text=f"Tool execution failed: {str(e)}")],
                isError=True
            )
    
    async def _handle_get_tickets(self, arguments: Dict[str, Any]) -> CallToolResult:
        """Handle afm_get_tickets tool call."""
        try:
            ticket_id = arguments.get("ticket_id")
            limit = arguments.get("limit")
            offset = arguments.get("offset")
            filters = arguments.get("filters")
            
            logger.info("Getting AFM tickets", ticket_id=ticket_id, limit=limit, offset=offset, filters=filters)
            
            result = await self.afm_client.get_tickets(
                ticket_id=ticket_id,
                limit=limit,
                offset=offset,
                filters=filters
            )
            
            # Convert result to JSON string for display
            if hasattr(result, 'dict'):
                result_dict = result.dict()
            else:
                result_dict = result
            
            result_json = json.dumps(result_dict, indent=2, default=str)
            
            if ticket_id:
                message = f"Retrieved AFM ticket {ticket_id}:\n{result_json}"
            else:
                ticket_count = len(result.tickets) if hasattr(result, 'tickets') else 0
                message = f"Retrieved {ticket_count} AFM tickets:\n{result_json}"
            
            return CallToolResult(
                content=[TextContent(type="text", text=message)]
            )
            
        except AFMClientError as e:
            logger.error("AFM API error in get_tickets", error=str(e), status_code=e.status_code)
            return CallToolResult(
                content=[TextContent(type="text", text=f"AFM API error: {str(e)}")],
                isError=True
            )
    
    async def _handle_insert_ticket(self, arguments: Dict[str, Any]) -> CallToolResult:
        """Handle afm_insert_ticket tool call."""
        try:
            # Validate and create ticket data
            ticket_data = AFMTicketCreate(**arguments)
            
            logger.info("Creating AFM ticket", ticket_data=ticket_data.dict())
            
            result = await self.afm_client.create_ticket(ticket_data)
            
            # Convert result to JSON string for display
            result_dict = result.dict() if hasattr(result, 'dict') else result
            result_json = json.dumps(result_dict, indent=2, default=str)
            
            message = f"Successfully created AFM ticket:\n{result_json}"
            
            return CallToolResult(
                content=[TextContent(type="text", text=message)]
            )
            
        except ValidationError as e:
            logger.error("Validation error in insert_ticket", error=str(e), arguments=arguments)
            return CallToolResult(
                content=[TextContent(type="text", text=f"Validation error: {str(e)}")],
                isError=True
            )
        
        except AFMClientError as e:
            logger.error("AFM API error in insert_ticket", error=str(e), status_code=e.status_code)
            return CallToolResult(
                content=[TextContent(type="text", text=f"AFM API error: {str(e)}")],
                isError=True
            )
    
    async def _handle_update_ticket(self, arguments: Dict[str, Any]) -> CallToolResult:
        """Handle afm_update_ticket tool call."""
        try:
            ticket_id = arguments.pop("ticket_id")
            
            if not arguments:
                return CallToolResult(
                    content=[TextContent(type="text", text="No update data provided")],
                    isError=True
                )
            
            # Validate and create update data
            ticket_data = AFMTicketUpdate(**arguments)
            
            logger.info("Updating AFM ticket", ticket_id=ticket_id, ticket_data=ticket_data.dict(exclude_none=True))
            
            result = await self.afm_client.update_ticket(ticket_id, ticket_data)
            
            # Convert result to JSON string for display
            result_dict = result.dict() if hasattr(result, 'dict') else result
            result_json = json.dumps(result_dict, indent=2, default=str)
            
            message = f"Successfully updated AFM ticket {ticket_id}:\n{result_json}"
            
            return CallToolResult(
                content=[TextContent(type="text", text=message)]
            )
            
        except ValidationError as e:
            logger.error("Validation error in update_ticket", error=str(e), arguments=arguments)
            return CallToolResult(
                content=[TextContent(type="text", text=f"Validation error: {str(e)}")],
                isError=True
            )
        
        except AFMClientError as e:
            logger.error("AFM API error in update_ticket", error=str(e), status_code=e.status_code)
            return CallToolResult(
                content=[TextContent(type="text", text=f"AFM API error: {str(e)}")],
                isError=True
            )
