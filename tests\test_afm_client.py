"""Tests for AFM client."""

import pytest
import httpx
from unittest.mock import AsyncMock, patch

from afm_mcp_server.afm_client import AFMClient, AFMClientError, AFMAuthenticationError
from afm_mcp_server.models import AFMTicketCreate, AFMTicketUpdate


@pytest.fixture
def afm_client():
    """Create AFM client for testing."""
    return AFMClient(
        base_url="https://test.example.com/api/v1",
        client_id="test_client_id",
        client_secret="test_client_secret",
        token_url="https://test.example.com/api/v1/oauth2/token",
    )


@pytest.mark.asyncio
async def test_authentication_success(afm_client):
    """Test successful authentication."""
    mock_response = AsyncMock()
    mock_response.json.return_value = {
        "access_token": "test_token",
        "token_type": "Bearer",
        "expires_in": 3600
    }
    mock_response.raise_for_status.return_value = None
    
    with patch.object(afm_client, '_http_client') as mock_client:
        mock_client.post.return_value = mock_response
        
        token = await afm_client._authenticate()
        
        assert token == "test_token"
        assert afm_client._access_token == "test_token"


@pytest.mark.asyncio
async def test_authentication_failure(afm_client):
    """Test authentication failure."""
    mock_response = AsyncMock()
    mock_response.status_code = 401
    mock_response.json.return_value = {"error": "invalid_client"}
    mock_response.text = "Unauthorized"
    
    with patch.object(afm_client, '_http_client') as mock_client:
        mock_client.post.side_effect = httpx.HTTPStatusError(
            "401 Unauthorized", request=None, response=mock_response
        )
        
        with pytest.raises(AFMAuthenticationError):
            await afm_client._authenticate()


@pytest.mark.asyncio
async def test_get_tickets_success(afm_client):
    """Test successful ticket retrieval."""
    # Mock authentication
    afm_client._access_token = "test_token"
    
    mock_response = AsyncMock()
    mock_response.json.return_value = [
        {
            "id": "1",
            "title": "Test Ticket",
            "description": "Test Description",
            "status": "Open"
        }
    ]
    mock_response.raise_for_status.return_value = None
    
    with patch.object(afm_client, '_http_client') as mock_client:
        mock_client.request.return_value = mock_response
        
        result = await afm_client.get_tickets()
        
        assert len(result.tickets) == 1
        assert result.tickets[0].id == "1"
        assert result.tickets[0].title == "Test Ticket"


@pytest.mark.asyncio
async def test_create_ticket_success(afm_client):
    """Test successful ticket creation."""
    # Mock authentication
    afm_client._access_token = "test_token"
    
    ticket_data = AFMTicketCreate(
        title="New Ticket",
        description="New Description",
        priority="High"
    )
    
    mock_response = AsyncMock()
    mock_response.json.return_value = {
        "id": "123",
        "title": "New Ticket",
        "description": "New Description",
        "priority": "High",
        "status": "Open"
    }
    mock_response.raise_for_status.return_value = None
    
    with patch.object(afm_client, '_http_client') as mock_client:
        mock_client.request.return_value = mock_response
        
        result = await afm_client.create_ticket(ticket_data)
        
        assert result.id == "123"
        assert result.title == "New Ticket"
        assert result.priority == "High"


@pytest.mark.asyncio
async def test_update_ticket_success(afm_client):
    """Test successful ticket update."""
    # Mock authentication
    afm_client._access_token = "test_token"
    
    ticket_data = AFMTicketUpdate(
        status="In Progress",
        assigned_to="John Doe"
    )
    
    mock_response = AsyncMock()
    mock_response.json.return_value = {
        "id": "123",
        "title": "Updated Ticket",
        "status": "In Progress",
        "assigned_to": "John Doe"
    }
    mock_response.raise_for_status.return_value = None
    
    with patch.object(afm_client, '_http_client') as mock_client:
        mock_client.request.return_value = mock_response
        
        result = await afm_client.update_ticket("123", ticket_data)
        
        assert result.id == "123"
        assert result.status == "In Progress"
        assert result.assigned_to == "John Doe"


@pytest.mark.asyncio
async def test_api_error_handling(afm_client):
    """Test API error handling."""
    # Mock authentication
    afm_client._access_token = "test_token"
    
    mock_response = AsyncMock()
    mock_response.status_code = 404
    mock_response.json.return_value = {"error": "Not Found"}
    mock_response.text = "Not Found"
    
    with patch.object(afm_client, '_http_client') as mock_client:
        mock_client.request.side_effect = httpx.HTTPStatusError(
            "404 Not Found", request=None, response=mock_response
        )
        
        with pytest.raises(AFMClientError) as exc_info:
            await afm_client.get_tickets("nonexistent")
        
        assert exc_info.value.status_code == 404
