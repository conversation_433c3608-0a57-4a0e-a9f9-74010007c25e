#!/usr/bin/env python3
"""
Test script pro ověření připojení k AFM API.
Tento script testuje základní funkcionalitu AFM klienta.
"""

import asyncio
import os
import sys
from dotenv import load_dotenv

# Přidej src do Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from afm_mcp_server.afm_client import AFMClient, AFMClientError
from afm_mcp_server.models import AFMTicketCreate

# Načti environment variables
load_dotenv()


async def test_afm_connection():
    """Test AFM API connection and basic operations."""
    
    # Konfigurace
    base_url = os.getenv("AFM_BASE_URL", "https://fmfullademo3.dev.alstanet.cz/api/v1")
    client_id = os.getenv("AFM_CLIENT_ID", "e0cc897a-5b99-4cc9-984c-6ee0581c4169")
    client_secret = os.getenv("AFM_CLIENT_SECRET", "DNfvkCXfsNGLKela6vFDMxjZcqPjSu0YxvxdZ5bGO0c=")
    token_url = os.getenv("AFM_TOKEN_URL", "https://fmfullademo3.dev.alstanet.cz/api/v1/oauth2/token")
    
    print("🔧 AFM MCP Server - Test připojení")
    print("=" * 50)
    print(f"Base URL: {base_url}")
    print(f"Client ID: {client_id}")
    print(f"Token URL: {token_url}")
    print()
    
    # Vytvoř AFM klienta
    client = AFMClient(
        base_url=base_url,
        client_id=client_id,
        client_secret=client_secret,
        token_url=token_url,
    )
    
    try:
        async with client:
            # Test 1: Autentifikace
            print("1️⃣ Testování autentifikace...")
            try:
                token = await client._authenticate()
                print(f"   ✅ Autentifikace úspěšná! Token: {token[:20]}...")
            except Exception as e:
                print(f"   ❌ Autentifikace selhala: {e}")
                return
            
            # Test 2: Získání ticketů
            print("\n2️⃣ Testování získání ticketů...")
            try:
                tickets = await client.get_tickets(limit=5)
                print(f"   ✅ Získáno {len(tickets.tickets)} ticketů")
                
                if tickets.tickets:
                    first_ticket = tickets.tickets[0]
                    print(f"   📋 První ticket: ID={first_ticket.id}, Title={first_ticket.title}")
                else:
                    print("   ℹ️ Žádné tickety nenalezeny")
                    
            except Exception as e:
                print(f"   ❌ Získání ticketů selhalo: {e}")
            
            # Test 3: Vytvoření testovacího ticketu
            print("\n3️⃣ Testování vytvoření ticketu...")
            try:
                test_ticket = AFMTicketCreate(
                    title="Test ticket z MCP serveru",
                    description="Tento ticket byl vytvořen automaticky během testování MCP serveru pro AFM.",
                    priority="Low",
                    category="Test",
                    reporter="MCP Server Test"
                )
                
                created_ticket = await client.create_ticket(test_ticket)
                print(f"   ✅ Ticket vytvořen! ID: {created_ticket.id}")
                print(f"   📋 Název: {created_ticket.title}")
                
                # Test 4: Aktualizace ticketu
                if created_ticket.id:
                    print("\n4️⃣ Testování aktualizace ticketu...")
                    try:
                        from afm_mcp_server.models import AFMTicketUpdate
                        
                        update_data = AFMTicketUpdate(
                            description="Aktualizovaný popis - test dokončen",
                            status="Closed"
                        )
                        
                        updated_ticket = await client.update_ticket(created_ticket.id, update_data)
                        print(f"   ✅ Ticket aktualizován! Status: {updated_ticket.status}")
                        
                    except Exception as e:
                        print(f"   ❌ Aktualizace ticketu selhala: {e}")
                
            except Exception as e:
                print(f"   ❌ Vytvoření ticketu selhalo: {e}")
            
            print("\n🎉 Test dokončen!")
            
    except Exception as e:
        print(f"❌ Obecná chyba: {e}")
        return False
    
    return True


async def main():
    """Main function."""
    success = await test_afm_connection()
    
    if success:
        print("\n✅ Všechny testy proběhly úspěšně!")
        print("🚀 AFM MCP Server je připraven k použití.")
    else:
        print("\n❌ Některé testy selhaly.")
        print("🔍 Zkontrolujte konfiguraci a síťové připojení.")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
