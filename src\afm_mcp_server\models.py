"""Pydantic models for AFM API data structures."""

from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class AFMTicket(BaseModel):
    """Model representing an AFM incident ticket."""
    
    id: Optional[str] = Field(None, description="Unique identifier of the ticket")
    title: Optional[str] = Field(None, description="Title/subject of the ticket")
    description: Optional[str] = Field(None, description="Detailed description of the incident")
    status: Optional[str] = Field(None, description="Current status of the ticket")
    priority: Optional[str] = Field(None, description="Priority level of the ticket")
    category: Optional[str] = Field(None, description="Category/type of the incident")
    created_date: Optional[datetime] = Field(None, description="Date when ticket was created")
    updated_date: Optional[datetime] = Field(None, description="Date when ticket was last updated")
    assigned_to: Optional[str] = Field(None, description="Person/team assigned to the ticket")
    reporter: Optional[str] = Field(None, description="Person who reported the incident")
    location: Optional[str] = Field(None, description="Location where incident occurred")
    
    class Config:
        """Pydantic configuration."""
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }


class AFMTicketCreate(BaseModel):
    """Model for creating a new AFM ticket."""
    
    title: str = Field(..., description="Title/subject of the ticket")
    description: str = Field(..., description="Detailed description of the incident")
    priority: Optional[str] = Field("Medium", description="Priority level (Low, Medium, High, Critical)")
    category: Optional[str] = Field(None, description="Category/type of the incident")
    location: Optional[str] = Field(None, description="Location where incident occurred")
    reporter: Optional[str] = Field(None, description="Person reporting the incident")


class AFMTicketUpdate(BaseModel):
    """Model for updating an existing AFM ticket."""
    
    title: Optional[str] = Field(None, description="Title/subject of the ticket")
    description: Optional[str] = Field(None, description="Detailed description of the incident")
    status: Optional[str] = Field(None, description="Current status of the ticket")
    priority: Optional[str] = Field(None, description="Priority level")
    category: Optional[str] = Field(None, description="Category/type of the incident")
    assigned_to: Optional[str] = Field(None, description="Person/team assigned to the ticket")
    location: Optional[str] = Field(None, description="Location where incident occurred")


class AFMTicketList(BaseModel):
    """Model for AFM ticket list response."""
    
    tickets: List[AFMTicket] = Field(default_factory=list, description="List of tickets")
    total_count: Optional[int] = Field(None, description="Total number of tickets")
    page: Optional[int] = Field(None, description="Current page number")
    page_size: Optional[int] = Field(None, description="Number of items per page")


class OAuthToken(BaseModel):
    """Model for OAuth 2.0 token response."""
    
    access_token: str = Field(..., description="Access token for API authentication")
    token_type: str = Field("Bearer", description="Type of the token")
    expires_in: Optional[int] = Field(None, description="Token expiration time in seconds")
    refresh_token: Optional[str] = Field(None, description="Refresh token for renewing access")
    scope: Optional[str] = Field(None, description="Token scope")


class AFMError(BaseModel):
    """Model for AFM API error responses."""
    
    error: str = Field(..., description="Error code or type")
    error_description: Optional[str] = Field(None, description="Human-readable error description")
    message: Optional[str] = Field(None, description="Detailed error message")
    status_code: Optional[int] = Field(None, description="HTTP status code")
