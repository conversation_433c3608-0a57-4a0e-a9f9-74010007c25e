"""Pydantic models for AFM API data structures."""

from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class AFMTicket(BaseModel):
    """Model representing an AFM incident ticket - matches AFM API structure."""

    # Primary fields - using AFM API field names
    IncidentID: Optional[int] = Field(None, description="Unique ticket identifier")
    Subject: Optional[str] = Field(None, description="Ticket title/subject")
    Description: Optional[str] = Field(None, description="Detailed description of the issue")
    StatusID: Optional[int] = Field(None, description="Current status ID of the ticket")
    PriorityID: Optional[int] = Field(None, description="Priority level ID")
    CategoryID: Optional[int] = Field(None, description="Ticket category ID")
    ActivityID: Optional[int] = Field(None, description="Activity type ID")

    # Date fields (AFM returns as strings)
    CreatedDate: Optional[str] = Field(None, description="When the ticket was created")
    UpdatedDate: Optional[str] = Field(None, description="When the ticket was last updated")
    OrderedDate: Optional[str] = Field(None, description="When the ticket was ordered")
    DispatchToDate: Optional[str] = Field(None, description="Dispatch deadline")
    RealizationDate: Optional[str] = Field(None, description="When the ticket was realized")
    StatusDate: Optional[str] = Field(None, description="When the status was last changed")

    # User/Employee fields
    CreatedByUserID: Optional[int] = Field(None, description="User who created the ticket")
    UpdatedByUserID: Optional[int] = Field(None, description="User who last updated the ticket")
    ResponsibleEmployeeID: Optional[int] = Field(None, description="Employee responsible for the ticket")
    OrderedByEmployeeID: Optional[int] = Field(None, description="Employee who ordered the work")
    DispatchByEmployeeID: Optional[int] = Field(None, description="Employee who dispatched the work")

    # Location fields
    ClientID: Optional[int] = Field(None, description="Client ID")
    SiteID: Optional[int] = Field(None, description="Site ID")
    BuildingID: Optional[int] = Field(None, description="Building ID")
    FloorID: Optional[int] = Field(None, description="Floor ID")
    RoomID: Optional[int] = Field(None, description="Room ID")

    # Status and contact information
    StatusComments: Optional[str] = Field(None, description="Comments about the current status")
    StatusContact: Optional[str] = Field(None, description="Contact person for status updates")
    StatusContactEmail: Optional[str] = Field(None, description="Contact email for status updates")
    StatusContactPhone: Optional[str] = Field(None, description="Contact phone for status updates")

    # Technology fields
    TchType1ID: Optional[int] = Field(None, description="Technology type 1 ID")
    TchType2ID: Optional[int] = Field(None, description="Technology type 2 ID")
    TchType3ID: Optional[int] = Field(None, description="Technology type 3 ID")
    TchCatalogID: Optional[int] = Field(None, description="Technology catalog ID")
    TechnologyID: Optional[int] = Field(None, description="Technology ID")

    # Other fields
    RevisionID: Optional[int] = Field(None, description="Revision ID")
    GID: Optional[str] = Field(None, description="Global identifier (e.g., AFM-123)")

    # Backward compatibility properties for existing MCP tools
    @property
    def id(self) -> Optional[str]:
        """Backward compatibility: return IncidentID as string"""
        return str(self.IncidentID) if self.IncidentID is not None else None

    @property
    def title(self) -> Optional[str]:
        """Backward compatibility: return Subject"""
        return self.Subject

    @property
    def description(self) -> Optional[str]:
        """Backward compatibility: return Description"""
        return self.Description

    @property
    def status(self) -> Optional[str]:
        """Backward compatibility: return StatusID as string"""
        return str(self.StatusID) if self.StatusID is not None else None

    @property
    def priority(self) -> Optional[str]:
        """Backward compatibility: return PriorityID as string"""
        return str(self.PriorityID) if self.PriorityID is not None else None

    @property
    def category(self) -> Optional[str]:
        """Backward compatibility: return CategoryID as string"""
        return str(self.CategoryID) if self.CategoryID is not None else None

    @property
    def created_date(self) -> Optional[str]:
        """Backward compatibility: return CreatedDate"""
        return self.CreatedDate

    @property
    def updated_date(self) -> Optional[str]:
        """Backward compatibility: return UpdatedDate"""
        return self.UpdatedDate

    @property
    def assigned_to(self) -> Optional[str]:
        """Backward compatibility: return ResponsibleEmployeeID as string"""
        return str(self.ResponsibleEmployeeID) if self.ResponsibleEmployeeID is not None else None

    @property
    def reporter(self) -> Optional[str]:
        """Backward compatibility: return CreatedByUserID as string"""
        return str(self.CreatedByUserID) if self.CreatedByUserID is not None else None

    @property
    def location(self) -> Optional[str]:
        """Backward compatibility: return location info as string"""
        parts = []
        if self.SiteID:
            parts.append(f"Site:{self.SiteID}")
        if self.BuildingID:
            parts.append(f"Building:{self.BuildingID}")
        if self.FloorID:
            parts.append(f"Floor:{self.FloorID}")
        if self.RoomID:
            parts.append(f"Room:{self.RoomID}")
        return " | ".join(parts) if parts else None


class AFMTicketCreate(BaseModel):
    """Model for creating a new AFM ticket - uses AFM API field names."""

    # Required fields
    Subject: str = Field(..., description="Title/subject of the ticket")
    Description: str = Field(..., description="Detailed description of the incident")

    # Optional fields with AFM API names
    PriorityID: Optional[int] = Field(None, description="Priority level ID")
    CategoryID: Optional[int] = Field(None, description="Category/type ID")
    ActivityID: Optional[int] = Field(None, description="Activity type ID")
    StatusID: Optional[int] = Field(None, description="Initial status ID")

    # Location fields
    ClientID: Optional[int] = Field(None, description="Client ID")
    SiteID: Optional[int] = Field(None, description="Site ID")
    BuildingID: Optional[int] = Field(None, description="Building ID")
    FloorID: Optional[int] = Field(None, description="Floor ID")
    RoomID: Optional[int] = Field(None, description="Room ID")

    # Employee fields
    ResponsibleEmployeeID: Optional[int] = Field(None, description="Employee responsible for the ticket")
    OrderedByEmployeeID: Optional[int] = Field(None, description="Employee who ordered the work")

    # Date fields (as strings to match AFM format)
    OrderedDate: Optional[str] = Field(None, description="When the ticket was ordered")
    DispatchToDate: Optional[str] = Field(None, description="Dispatch deadline")

    # Technology fields
    TchType1ID: Optional[int] = Field(None, description="Technology type 1 ID")
    TchType2ID: Optional[int] = Field(None, description="Technology type 2 ID")
    TchType3ID: Optional[int] = Field(None, description="Technology type 3 ID")
    TchCatalogID: Optional[int] = Field(None, description="Technology catalog ID")
    TechnologyID: Optional[int] = Field(None, description="Technology ID")

    # Backward compatibility properties
    @property
    def title(self) -> str:
        """Backward compatibility: return Subject"""
        return self.Subject

    @property
    def description(self) -> str:
        """Backward compatibility: return Description"""
        return self.Description


class AFMTicketUpdate(BaseModel):
    """Model for updating an existing AFM ticket - uses AFM API field names."""

    # Basic fields
    Subject: Optional[str] = Field(None, description="Title/subject of the ticket")
    Description: Optional[str] = Field(None, description="Detailed description of the incident")
    StatusID: Optional[int] = Field(None, description="Current status ID of the ticket")
    PriorityID: Optional[int] = Field(None, description="Priority level ID")
    CategoryID: Optional[int] = Field(None, description="Category/type ID")
    ActivityID: Optional[int] = Field(None, description="Activity type ID")

    # Employee fields
    ResponsibleEmployeeID: Optional[int] = Field(None, description="Employee responsible for the ticket")
    DispatchByEmployeeID: Optional[int] = Field(None, description="Employee who dispatched the work")

    # Location fields
    ClientID: Optional[int] = Field(None, description="Client ID")
    SiteID: Optional[int] = Field(None, description="Site ID")
    BuildingID: Optional[int] = Field(None, description="Building ID")
    FloorID: Optional[int] = Field(None, description="Floor ID")
    RoomID: Optional[int] = Field(None, description="Room ID")

    # Date fields
    DispatchToDate: Optional[str] = Field(None, description="Dispatch deadline")
    RealizationDate: Optional[str] = Field(None, description="When the ticket was realized")

    # Status information
    StatusComments: Optional[str] = Field(None, description="Comments about the current status")
    StatusContact: Optional[str] = Field(None, description="Contact person for status updates")
    StatusContactEmail: Optional[str] = Field(None, description="Contact email for status updates")
    StatusContactPhone: Optional[str] = Field(None, description="Contact phone for status updates")

    # Technology fields
    TchType1ID: Optional[int] = Field(None, description="Technology type 1 ID")
    TchType2ID: Optional[int] = Field(None, description="Technology type 2 ID")
    TchType3ID: Optional[int] = Field(None, description="Technology type 3 ID")
    TchCatalogID: Optional[int] = Field(None, description="Technology catalog ID")
    TechnologyID: Optional[int] = Field(None, description="Technology ID")

    # Other fields
    RevisionID: Optional[int] = Field(None, description="Revision ID")


class AFMTicketList(BaseModel):
    """Model for AFM ticket list response."""
    
    tickets: List[AFMTicket] = Field(default_factory=list, description="List of tickets")
    total_count: Optional[int] = Field(None, description="Total number of tickets")
    page: Optional[int] = Field(None, description="Current page number")
    page_size: Optional[int] = Field(None, description="Number of items per page")


class OAuthToken(BaseModel):
    """Model for OAuth 2.0 token response."""
    
    access_token: str = Field(..., description="Access token for API authentication")
    token_type: str = Field("Bearer", description="Type of the token")
    expires_in: Optional[int] = Field(None, description="Token expiration time in seconds")
    refresh_token: Optional[str] = Field(None, description="Refresh token for renewing access")
    scope: Optional[str] = Field(None, description="Token scope")


class AFMError(BaseModel):
    """Model for AFM API error responses."""
    
    error: str = Field(..., description="Error code or type")
    error_description: Optional[str] = Field(None, description="Human-readable error description")
    message: Optional[str] = Field(None, description="Detailed error message")
    status_code: Optional[int] = Field(None, description="HTTP status code")
