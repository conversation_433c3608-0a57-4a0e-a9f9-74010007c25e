"""AFM REST API client with OAuth 2.0 authentication."""

import asyncio
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union

import httpx
import structlog
from pydantic import ValidationError

from .models import (
    AFMError,
    AFMTicket,
    AFMTicketCreate,
    AFMTicketList,
    AFMTicketUpdate,
    OAuthToken,
)

logger = structlog.get_logger(__name__)


class AFMClientError(Exception):
    """Base exception for AFM client errors."""
    
    def __init__(self, message: str, status_code: Optional[int] = None, response_data: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.status_code = status_code
        self.response_data = response_data


class AFMAuthenticationError(AFMClientError):
    """Exception raised when authentication fails."""
    pass


class AFMClient:
    """Asynchronous client for AFM REST API with OAuth 2.0 authentication."""
    
    def __init__(
        self,
        base_url: str,
        client_id: str,
        client_secret: str,
        token_url: str,
        timeout: float = 30.0,
    ):
        """Initialize AFM client.
        
        Args:
            base_url: Base URL of the AFM API
            client_id: OAuth 2.0 client ID
            client_secret: OAuth 2.0 client secret
            token_url: OAuth 2.0 token endpoint URL
            timeout: Request timeout in seconds
        """
        self.base_url = base_url.rstrip("/")
        self.client_id = client_id
        self.client_secret = client_secret
        self.token_url = token_url
        self.timeout = timeout
        
        self._http_client: Optional[httpx.AsyncClient] = None
        self._access_token: Optional[str] = None
        self._token_expires_at: Optional[datetime] = None
        self._auth_lock = asyncio.Lock()
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self._ensure_http_client()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()
    
    async def _ensure_http_client(self):
        """Ensure HTTP client is initialized."""
        if self._http_client is None:
            self._http_client = httpx.AsyncClient(
                timeout=httpx.Timeout(self.timeout),
                headers={"Content-Type": "application/json"},
            )
    
    async def close(self):
        """Close the HTTP client."""
        if self._http_client:
            await self._http_client.aclose()
            self._http_client = None
    
    async def _authenticate(self) -> str:
        """Authenticate with AFM API and get access token.
        
        Returns:
            Access token string
            
        Raises:
            AFMAuthenticationError: If authentication fails
        """
        async with self._auth_lock:
            # Check if we have a valid token
            if (
                self._access_token
                and self._token_expires_at
                and datetime.now() < self._token_expires_at - timedelta(minutes=5)
            ):
                return self._access_token
            
            await self._ensure_http_client()
            
            # Prepare OAuth 2.0 client credentials request
            auth_data = {
                "grant_type": "client_credentials",
                "client_id": self.client_id,
                "client_secret": self.client_secret,
                "scope": "afmmaintenance",  # AFM API requires scope parameter
            }

            try:
                logger.info("Authenticating with AFM API", token_url=self.token_url)
                response = await self._http_client.post(
                    self.token_url,
                    data=auth_data,
                    headers={"Content-Type": "application/x-www-form-urlencoded"},
                )
                response.raise_for_status()

                token_data = response.json()
                logger.debug("OAuth response received", response_data=token_data)
                token = OAuthToken(**token_data)
                
                self._access_token = token.access_token
                if token.expires_in:
                    self._token_expires_at = datetime.now() + timedelta(seconds=token.expires_in)
                
                logger.info("Successfully authenticated with AFM API")
                return self._access_token
                
            except httpx.HTTPStatusError as e:
                error_msg = f"Authentication failed with status {e.response.status_code}"
                try:
                    error_data = e.response.json()
                    error_msg += f": {error_data.get('error_description', error_data)}"
                except Exception:
                    error_msg += f": {e.response.text}"
                
                logger.error("AFM authentication failed", error=error_msg, status_code=e.response.status_code)
                raise AFMAuthenticationError(error_msg, e.response.status_code, e.response.json() if e.response.text else None)
            
            except Exception as e:
                error_msg = f"Authentication request failed: {str(e)}"
                logger.error("AFM authentication request failed", error=str(e))
                raise AFMAuthenticationError(error_msg)
    
    async def _make_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """Make authenticated request to AFM API.
        
        Args:
            method: HTTP method (GET, POST, PUT, DELETE)
            endpoint: API endpoint path
            data: Request body data
            params: Query parameters
            
        Returns:
            Response data as dictionary
            
        Raises:
            AFMClientError: If request fails
        """
        await self._ensure_http_client()
        access_token = await self._authenticate()
        
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        headers = {"Authorization": f"Bearer {access_token}"}
        
        try:
            logger.debug("Making AFM API request", method=method, url=url, params=params)
            
            response = await self._http_client.request(
                method=method,
                url=url,
                json=data if data else None,
                params=params,
                headers=headers,
            )
            
            # Handle different response status codes
            if response.status_code == 204:  # No Content
                return {}
            
            response.raise_for_status()
            
            # Try to parse JSON response
            try:
                response_data = response.json()
                logger.debug("AFM API request successful", status_code=response.status_code)
                return response_data
            except Exception:
                # If not JSON, return text content
                return {"content": response.text}
                
        except httpx.HTTPStatusError as e:
            error_msg = f"AFM API request failed with status {e.response.status_code}"
            try:
                error_data = e.response.json()
                if isinstance(error_data, dict):
                    error_msg += f": {error_data.get('message', error_data.get('error', error_data))}"
                else:
                    error_msg += f": {error_data}"
            except Exception:
                error_msg += f": {e.response.text}"
            
            logger.error("AFM API request failed", error=error_msg, status_code=e.response.status_code, url=url)
            raise AFMClientError(error_msg, e.response.status_code, e.response.json() if e.response.text else None)
        
        except Exception as e:
            error_msg = f"AFM API request failed: {str(e)}"
            logger.error("AFM API request failed", error=str(e), url=url)
            raise AFMClientError(error_msg)

    # Ticket management methods

    async def get_tickets(
        self,
        ticket_id: Optional[str] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        filters: Optional[Dict[str, Any]] = None,
    ) -> Union[AFMTicket, AFMTicketList]:
        """Get tickets from AFM API.

        Args:
            ticket_id: Specific ticket ID to retrieve (if None, gets all tickets)
            limit: Maximum number of tickets to return
            offset: Number of tickets to skip
            filters: Additional filters for ticket search

        Returns:
            Single ticket if ticket_id provided, otherwise list of tickets

        Raises:
            AFMClientError: If request fails
        """
        endpoint = "util/datasource/afmmaintenance_incident"

        if ticket_id:
            # Get specific ticket
            endpoint = f"{endpoint}/{ticket_id}"
            response_data = await self._make_request("GET", endpoint)

            try:
                return AFMTicket(**response_data)
            except ValidationError as e:
                logger.warning("Failed to parse AFM ticket response", error=str(e), data=response_data)
                # Return raw data if parsing fails
                return AFMTicket(id=ticket_id, **{k: v for k, v in response_data.items() if k != 'id'})

        else:
            # Get list of tickets
            params = {}
            if limit:
                params["limit"] = limit
            if offset:
                params["offset"] = offset
            if filters:
                params.update(filters)

            response_data = await self._make_request("GET", endpoint, params=params)

            # Handle different response formats
            if isinstance(response_data, list):
                tickets = []
                for ticket_data in response_data:
                    try:
                        tickets.append(AFMTicket(**ticket_data))
                    except ValidationError as e:
                        logger.warning("Failed to parse AFM ticket", error=str(e), data=ticket_data)
                        # Create ticket with available data
                        tickets.append(AFMTicket(**{k: v for k, v in ticket_data.items() if k in AFMTicket.__fields__}))

                return AFMTicketList(tickets=tickets, total_count=len(tickets))

            elif isinstance(response_data, dict):
                # Handle paginated response
                tickets_data = response_data.get("items", response_data.get("data", [response_data]))
                if not isinstance(tickets_data, list):
                    tickets_data = [tickets_data]

                tickets = []
                for ticket_data in tickets_data:
                    try:
                        tickets.append(AFMTicket(**ticket_data))
                    except ValidationError as e:
                        logger.warning("Failed to parse AFM ticket", error=str(e), data=ticket_data)
                        tickets.append(AFMTicket(**{k: v for k, v in ticket_data.items() if k in AFMTicket.__fields__}))

                return AFMTicketList(
                    tickets=tickets,
                    total_count=response_data.get("total", len(tickets)),
                    page=response_data.get("page"),
                    page_size=response_data.get("page_size"),
                )

            else:
                return AFMTicketList(tickets=[], total_count=0)

    async def create_ticket(self, ticket_data: AFMTicketCreate) -> AFMTicket:
        """Create a new ticket in AFM.

        Args:
            ticket_data: Ticket data to create

        Returns:
            Created ticket

        Raises:
            AFMClientError: If creation fails
        """
        endpoint = "util/datasource/afmmaintenance_incident"

        # Convert Pydantic model to dict, excluding None values
        data = ticket_data.dict(exclude_none=True)

        logger.info("Creating AFM ticket", data=data)
        response_data = await self._make_request("POST", endpoint, data=data)

        try:
            return AFMTicket(**response_data)
        except ValidationError as e:
            logger.warning("Failed to parse created AFM ticket response", error=str(e), data=response_data)
            # Return ticket with available data
            return AFMTicket(**{k: v for k, v in response_data.items() if k in AFMTicket.__fields__})

    async def update_ticket(self, ticket_id: str, ticket_data: AFMTicketUpdate) -> AFMTicket:
        """Update an existing ticket in AFM.

        Args:
            ticket_id: ID of the ticket to update
            ticket_data: Updated ticket data

        Returns:
            Updated ticket

        Raises:
            AFMClientError: If update fails
        """
        endpoint = f"util/datasource/afmmaintenance_incident/{ticket_id}"

        # Convert Pydantic model to dict, excluding None values
        data = ticket_data.dict(exclude_none=True)

        if not data:
            raise AFMClientError("No data provided for ticket update")

        logger.info("Updating AFM ticket", ticket_id=ticket_id, data=data)
        response_data = await self._make_request("PUT", endpoint, data=data)

        try:
            return AFMTicket(**response_data)
        except ValidationError as e:
            logger.warning("Failed to parse updated AFM ticket response", error=str(e), data=response_data)
            # Return ticket with available data
            return AFMTicket(id=ticket_id, **{k: v for k, v in response_data.items() if k != 'id'})

    async def delete_ticket(self, ticket_id: str) -> bool:
        """Delete a ticket from AFM.

        Args:
            ticket_id: ID of the ticket to delete

        Returns:
            True if deletion was successful

        Raises:
            AFMClientError: If deletion fails
        """
        endpoint = f"businessobject/fm.mnt/incident/{ticket_id}"

        logger.info("Deleting AFM ticket", ticket_id=ticket_id)
        await self._make_request("DELETE", endpoint)

        return True
