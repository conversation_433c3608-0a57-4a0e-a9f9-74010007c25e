"""AFM MCP Server - Main server implementation."""

import asyncio
import os
import sys
from typing import Any, Dict, Sequence

import structlog
from dotenv import load_dotenv
from mcp.server import Server
from mcp.server.lowlevel import NotificationOptions
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.server.sse import SseServerTransport
from mcp.types import Resource, Tool
from starlette.applications import Starlette
from starlette.routing import Route, Mount
from starlette.responses import JSONResponse
import uvicorn

from .afm_client import AFMClient
from .tools import AFMTools

# Load environment variables
load_dotenv()

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)


class AFMMCPServer:
    """AFM MCP Server implementation."""
    
    def __init__(self):
        """Initialize AFM MCP Server."""
        # Get configuration from environment variables
        self.afm_base_url = os.getenv("AFM_BASE_URL", "https://fmfullademo3.dev.alstanet.cz/api/v1")
        self.afm_client_id = os.getenv("AFM_CLIENT_ID", "e0cc897a-5b99-4cc9-984c-6ee0581c4169")
        self.afm_client_secret = os.getenv("AFM_CLIENT_SECRET", "DNfvkCXfsNGLKela6vFDMxjZcgPisu0Yvxvd25bGO0c=")
        self.afm_token_url = os.getenv("AFM_TOKEN_URL", "https://fmfullademo3.dev.alstanet.cz/api/v1/oauth2/token")
        
        self.server_name = os.getenv("MCP_SERVER_NAME", "afm-mcp-server")
        self.server_version = os.getenv("MCP_SERVER_VERSION", "0.1.0")
        
        # Set log level
        log_level = os.getenv("LOG_LEVEL", "INFO").upper()
        structlog.configure(
            processors=structlog.get_config()["processors"],
            wrapper_class=structlog.stdlib.BoundLogger,
            logger_factory=structlog.stdlib.LoggerFactory(),
            cache_logger_on_first_use=True,
        )
        
        # Initialize components
        self.afm_client: AFMClient = None
        self.afm_tools: AFMTools = None
        self.server = Server(self.server_name)
        
        # Register handlers
        self._register_handlers()
    
    def _register_handlers(self):
        """Register MCP server handlers."""
        
        @self.server.list_tools()
        async def handle_list_tools() -> list[Tool]:
            """List available tools."""
            if not self.afm_tools:
                return []
            return self.afm_tools.get_tools()
        
        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: dict[str, Any] | None) -> list[Any]:
            """Handle tool calls."""
            if not self.afm_tools:
                raise RuntimeError("AFM tools not initialized")

            if arguments is None:
                arguments = {}

            result = await self.afm_tools.handle_tool_call(name, arguments)
            # Return the content from CallToolResult, not the CallToolResult itself
            return result.content
        
        @self.server.list_resources()
        async def handle_list_resources() -> list[Resource]:
            """List available resources."""
            # For now, we don't expose any resources
            # Could be extended to expose AFM data as resources
            return []
    
    async def _initialize_clients(self):
        """Initialize AFM client and tools."""
        try:
            logger.info("Initializing AFM client", base_url=self.afm_base_url)
            
            self.afm_client = AFMClient(
                base_url=self.afm_base_url,
                client_id=self.afm_client_id,
                client_secret=self.afm_client_secret,
                token_url=self.afm_token_url,
            )
            
            self.afm_tools = AFMTools(self.afm_client)
            
            # Test authentication
            async with self.afm_client:
                await self.afm_client._authenticate()
                logger.info("AFM client initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize AFM client", error=str(e))
            raise
    
    async def run(self, test_mode: bool = False, http_mode: bool = False, port: int = 1984):
        """Run the MCP server."""
        try:
            logger.info("Starting AFM MCP Server", version=self.server_version)

            # Initialize clients
            await self._initialize_clients()

            if test_mode:
                print("AFM MCP Server running in test mode - initialization successful")
                logger.info("AFM MCP Server running in test mode - initialization successful")

                tools = self.afm_tools.get_tools()
                print(f"Available tools: {[tool.name for tool in tools]}")
                logger.info("Available tools:", tools=[tool.name for tool in tools])

                # Test AFM API connection
                try:
                    print("Testing AFM API connection...")
                    logger.info("Testing AFM API connection...")
                    test_result = await self.afm_tools._handle_get_tickets({"limit": 1})
                    print(f"AFM API connection test successful: {type(test_result).__name__}")
                    logger.info("AFM API connection test successful", result_type=type(test_result).__name__)
                except Exception as e:
                    print(f"AFM API connection test failed: {str(e)}")
                    logger.error("AFM API connection test failed", error=str(e))

                print("Test mode completed successfully")
                logger.info("Test mode completed successfully")
                return

            if http_mode:
                # Run server with HTTP/SSE transport
                logger.info(f"AFM MCP Server running on HTTP/SSE port {port}")

                # Create SSE transport
                sse = SseServerTransport("/messages")

                # Create Starlette app
                async def health_check(request):
                    return JSONResponse({"status": "healthy", "server": self.server_name, "version": self.server_version})

                async def handle_sse(request):
                    """Handle SSE connections."""
                    async with sse.connect_sse(
                        request.scope, request.receive, request._send
                    ) as streams:
                        await self.server.run(
                            streams[0],
                            streams[1],
                            InitializationOptions(
                                server_name=self.server_name,
                                server_version=self.server_version,
                                capabilities=self.server.get_capabilities(
                                    notification_options=NotificationOptions(),
                                    experimental_capabilities={}
                                ),
                            )
                        )

                app = Starlette(
                    routes=[
                        Route("/health", health_check),
                        Route("/sse", handle_sse),
                        Mount("/messages", app=sse.handle_post_message),
                    ]
                )

                # Run with uvicorn
                config = uvicorn.Config(app, host="0.0.0.0", port=port, log_level="info")
                server = uvicorn.Server(config)
                await server.serve()
            else:
                # Run server with stdio transport
                async with stdio_server() as (read_stream, write_stream):
                    logger.info("AFM MCP Server running on stdio")
                    await self.server.run(
                        read_stream,
                        write_stream,
                        InitializationOptions(
                            server_name=self.server_name,
                            server_version=self.server_version,
                            capabilities=self.server.get_capabilities(
                                notification_options=NotificationOptions(),
                                experimental_capabilities={}
                            ),
                        ),
                    )
        
        except KeyboardInterrupt:
            logger.info("AFM MCP Server stopped by user")
        
        except Exception as e:
            logger.error("AFM MCP Server error", error=str(e))
            raise
        
        finally:
            # Cleanup
            if self.afm_client:
                await self.afm_client.close()
            logger.info("AFM MCP Server shutdown complete")


async def main():
    """Main entry point."""
    try:
        # Check if running in test mode (Docker container without stdio client)
        test_mode = os.getenv("AFM_TEST_MODE", "false").lower() == "true"

        # Check if running in HTTP mode
        http_mode = os.getenv("AFM_HTTP_MODE", "false").lower() == "true"
        port = int(os.getenv("AFM_PORT", "1984"))

        print(f"Starting AFM MCP Server (test_mode={test_mode}, http_mode={http_mode}, port={port})")

        server = AFMMCPServer()
        await server.run(test_mode=test_mode, http_mode=http_mode, port=port)

    except Exception as e:
        logger.error("Failed to start AFM MCP Server", error=str(e))
        sys.exit(1)


def cli_main():
    """CLI entry point."""
    asyncio.run(main())


if __name__ == "__main__":
    cli_main()
