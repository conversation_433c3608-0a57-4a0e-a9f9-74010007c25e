version: '3.8'

services:
  afm-mcp-server:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: afm-mcp-server
    restart: unless-stopped
    
    # Environment variables (can be overridden with .env file)
    environment:
      - AFM_BASE_URL=${AFM_BASE_URL:-https://fmfullademo3.dev.alstanet.cz/api/v1}
      - AFM_CLIENT_ID=${AFM_CLIENT_ID:-e0cc897a-5b99-4cc9-984c-6ee0581c4169}
      - AFM_CLIENT_SECRET=${AFM_CLIENT_SECRET:-DNfvkCXfsNGLKela6vFDMxjZcqPjSu0YxvxdZ5bGO0c=}
      - AFM_TOKEN_URL=${AFM_TOKEN_URL:-https://fmfullademo3.dev.alstanet.cz/api/v1/oauth2/token}
      - MCP_SERVER_NAME=${MCP_SERVER_NAME:-afm-mcp-server}
      - MCP_SERVER_VERSION=${MCP_SERVER_VERSION:-0.1.0}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
    
    # Use stdin/stdout for MCP communication
    stdin_open: true
    tty: true
    
    # Optional: Mount logs directory
    volumes:
      - ./logs:/app/logs
    
    # Network configuration
    networks:
      - afm-network
    
    # Health check
    healthcheck:
      test: ["CMD", "python", "-c", "import sys; sys.exit(0)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  # Optional: Add a development/testing service
  afm-mcp-dev:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: afm-mcp-dev
    restart: "no"
    
    environment:
      - AFM_BASE_URL=${AFM_BASE_URL:-https://fmfullademo3.dev.alstanet.cz/api/v1}
      - AFM_CLIENT_ID=${AFM_CLIENT_ID:-e0cc897a-5b99-4cc9-984c-6ee0581c4169}
      - AFM_CLIENT_SECRET=${AFM_CLIENT_SECRET:-DNfvkCXfsNGLKela6vFDMxjZcqPjSu0YxvxdZ5bGO0c=}
      - AFM_TOKEN_URL=${AFM_TOKEN_URL:-https://fmfullademo3.dev.alstanet.cz/api/v1/oauth2/token}
      - LOG_LEVEL=DEBUG
    
    # Mount source code for development
    volumes:
      - ./src:/app/src
      - ./tests:/app/tests
      - ./logs:/app/logs
    
    # Override command for development
    command: ["python", "-m", "afm_mcp_server.server"]
    
    stdin_open: true
    tty: true
    
    networks:
      - afm-network
    
    profiles:
      - dev

networks:
  afm-network:
    driver: bridge

volumes:
  logs:
    driver: local
