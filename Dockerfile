# Use Python 3.11 slim image as base
FROM python:3.11-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Set work directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy project files
COPY pyproject.toml .
COPY src/ src/

# Install the package in development mode
RUN pip install -e .

# Create non-root user for security
RUN useradd --create-home --shell /bin/bash afm && \
    chown -R afm:afm /app
USER afm

# Set default environment variables
ENV AFM_BASE_URL=https://fmfullademo3.dev.alstanet.cz/api/v1 \
    AFM_CLIENT_ID=e0cc897a-5b99-4cc9-984c-6ee0581c4169 \
    AFM_CLIENT_SECRET=DNfvkCXfsNGLKela6vFDMxjZcgPisu0Yvxvd25bGO0c= \
    AFM_TOKEN_URL=https://fmfullademo3.dev.alstanet.cz/api/v1/oauth2/token \
    MCP_SERVER_NAME=afm-mcp-server \
    MCP_SERVER_VERSION=0.1.0 \
    LOG_LEVEL=INFO \
    AFM_HTTP_MODE=true \
    AFM_PORT=1984

# Expose port for HTTP/SSE server
EXPOSE 1984

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import sys; sys.exit(0)"

# Default command
CMD ["afm-mcp-server"]
